#!/bin/bash

# Error Code Verification Script for Handbag API
BASE_URL="http://localhost:5038"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Error Code Format Verification${NC}"
echo -e "${BLUE}Base URL: $BASE_URL${NC}\n"

# Get admin token
echo -e "${YELLOW}Getting admin token...${NC}"
AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "123456"}')

ADMIN_TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo -e "Token obtained: ${ADMIN_TOKEN:0:50}...\n"

# Test HB40001 - Missing/invalid input
echo -e "${BLUE}Testing HB40001 - Missing/invalid input${NC}"
RESPONSE=$(curl -s -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "invalid model", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

echo "Response: $RESPONSE"
if [[ $RESPONSE == *"HB40001"* && $RESPONSE == *"Missing/invalid input"* ]]; then
    echo -e "${GREEN}✅ HB40001 format correct${NC}\n"
else
    echo -e "${RED}❌ HB40001 format incorrect${NC}\n"
fi

# Test HB40101 - Token missing/invalid
echo -e "${BLUE}Testing HB40101 - Token missing/invalid${NC}"
RESPONSE=$(curl -s -X GET "$BASE_URL/api/handbags")

echo "Response: $RESPONSE"
if [[ $RESPONSE == *"HB40101"* && $RESPONSE == *"Token missing/invalid"* ]]; then
    echo -e "${GREEN}✅ HB40101 format correct${NC}\n"
else
    echo -e "${RED}❌ HB40101 format incorrect${NC}\n"
fi

# Test HB40301 - Permission denied (using member token)
echo -e "${BLUE}Testing HB40301 - Permission denied${NC}"
MEMBER_AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "123456"}')

MEMBER_TOKEN=$(echo "$MEMBER_AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

RESPONSE=$(curl -s -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $MEMBER_TOKEN" \
    -d '{"modelName": "Test Bag", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

echo "Response: $RESPONSE"
if [[ $RESPONSE == *"HB40301"* && $RESPONSE == *"Permission denied"* ]]; then
    echo -e "${GREEN}✅ HB40301 format correct${NC}\n"
else
    echo -e "${RED}❌ HB40301 format incorrect${NC}\n"
fi

# Test HB40401 - Resource not found
echo -e "${BLUE}Testing HB40401 - Resource not found${NC}"
RESPONSE=$(curl -s -X GET "$BASE_URL/api/handbags/99999" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

echo "Response: $RESPONSE"
if [[ $RESPONSE == *"HB40401"* && $RESPONSE == *"Resource not found"* ]]; then
    echo -e "${GREEN}✅ HB40401 format correct${NC}\n"
else
    echo -e "${RED}❌ HB40401 format incorrect${NC}\n"
fi

# Test invalid login for HB40401
echo -e "${BLUE}Testing HB40401 - Invalid login${NC}"
RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "wrongpassword"}')

echo "Response: $RESPONSE"
if [[ $RESPONSE == *"HB40401"* && $RESPONSE == *"Resource not found"* ]]; then
    echo -e "${GREEN}✅ HB40401 format correct for invalid login${NC}\n"
else
    echo -e "${RED}❌ HB40401 format incorrect for invalid login${NC}\n"
fi

echo -e "${BLUE}📊 Error Code Verification Complete${NC}"
echo -e "${YELLOW}All error responses should follow the format:${NC}"
echo -e '{"errorCode": "HBxxxxx", "message": "Description"}'
