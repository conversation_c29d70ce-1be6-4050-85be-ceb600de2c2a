{"info": {"_postman_id": "ce72ed4b-c1c0-41de-916c-8404f39533a2", "name": "API Tests - PRN231_SU25_SE170115", "description": "Comprehensive test collection for Handbag API with JWT authentication - Optimized with variables", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "36442572"}, "item": [{"name": "1. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Test 1: <PERSON><PERSON>", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has token and role', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "    pm.expect(responseJson).to.have.property('role');", "    pm.expect(responseJson.role).to.eql('administrator');", "});", "", "pm.test('Token is valid JWT format', function () {", "    const responseJson = pm.response.json();", "    const token = responseJson.token;", "    pm.expect(token).to.match(/^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+$/);", "});", "", "// Store token for subsequent requests", "pm.test('Token storage', function () {", "    if (pm.response.code === 200) {", "        const responseJson = pm.response.json();", "        if (responseJson.token) {", "            // <PERSON><PERSON><PERSON> vào biến toàn cục", "            pm.globals.set('token', responseJson.token);", "            pm.globals.set('user_role', responseJson.role);", "            console.log('✅ Token stored globally:', responseJson.token.substring(0, 20) + '...');", "            console.log('✅ User role:', responseJson.role);", "        } else {", "            console.log('❌ No token found in response');", "        }", "    } else {", "        console.log('❌ <PERSON><PERSON> failed, status:', pm.response.code);", "    }", "});", "", "// Verify token is stored globally", "pm.test('Token is stored globally', function () {", "    const storedToken = pm.globals.get('token');", "    pm.expect(storedToken).to.not.be.undefined;", "    pm.expect(storedToken).to.not.be.empty;", "    console.log('✅ Verified global token:', storedToken ? 'Present' : 'Missing');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}, "response": []}, {"name": "2. <PERSON><PERSON> Failed", "event": [{"listen": "test", "script": {"exec": ["// Test 2: <PERSON><PERSON> Failed", "pm.test('Status code is 500 (Internal Server Error due to exception handling)', function () {", "    pm.response.to.have.status(500);", "});", "", "pm.test('Response indicates error', function () {", "    // The API throws InternalServerErrorException for failed login", "    pm.expect(pm.response.code).to.be.oneOf([404, 500]);", "});", "", "pm.test('No token in response', function () {", "    try {", "        const responseJson = pm.response.json();", "        pm.expect(response<PERSON><PERSON>).to.not.have.property('token');", "    } catch (e) {", "        // Response might not be JSON for error cases", "        pm.expect(true).to.be.true;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/auth", "host": ["{{base_url}}"], "path": ["api", "auth"]}}, "response": []}, {"name": "3. <PERSON><PERSON>bag", "event": [{"listen": "test", "script": {"exec": ["// Test 3: Create Handbag Success", "pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "    // Store created handbag ID for update and delete operations", "    if (responseJson.handbagId || responseJson.id) {", "        const newId = responseJson.handbagId || responseJson.id;", "        pm.environment.set('created_handbag_id', newId);", "        pm.environment.set('handbag_id_to_update', newId);", "        pm.environment.set('handbag_id_to_delete', newId);", "        console.log('Created handbag ID stored:', newId);", "    }", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "    const globalToken = pm.globals.get('token');", "    pm.expect(globalToken).to.not.be.undefined;", "    console.log('✅ Using global token for request');", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Test Luxury #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 299.99,\n  \"stock\": 15,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}, "response": []}, {"name": "4. Update Handbag", "event": [{"listen": "test", "script": {"exec": ["// Test 4: Update Handbag Success", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message or updated handbag', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "    const globalToken = pm.globals.get('token');", "    pm.expect(globalToken).to.not.be.undefined;", "    console.log('✅ Using global token for request');", "});", "", "pm.test('Request method is PUT', function () {", "    pm.expect(pm.request.method).to.eql('PUT');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Updated Luxury #Bag\",\n  \"material\": \"Premium Leather\",\n  \"color\": \"Black\",\n  \"price\": 349.99,\n  \"stock\": 20,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags/{{handbag_id_to_update}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{handbag_id_to_update}}"]}}, "response": []}, {"name": "5. <PERSON><PERSON> Handbag", "event": [{"listen": "test", "script": {"exec": ["// Test 5: Delete Handbag Success", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('Deleted successfully');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "    const globalToken = pm.globals.get('token');", "    pm.expect(globalToken).to.not.be.undefined;", "    console.log('✅ Using global token for request');", "});", "", "pm.test('Request method is DELETE', function () {", "    pm.expect(pm.request.method).to.eql('DELETE');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/handbags/{{handbag_id_to_delete}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{handbag_id_to_delete}}"]}}, "response": []}, {"name": "6a. Get All Handbags", "event": [{"listen": "test", "script": {"exec": ["// Test 6a: Get All Handbags", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array of items', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "    const globalToken = pm.globals.get('token');", "    pm.expect(globalToken).to.not.be.undefined;", "    console.log('✅ Using global token for request');", "});", "", "pm.test('Request method is GET', function () {", "    pm.expect(pm.request.method).to.eql('GET');", "});", "", "// Store first handbag ID for subsequent tests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        const firstId = responseJson[0].handbagId || responseJson[0].id;", "        pm.environment.set('existing_handbag_id', firstId);", "        console.log('First handbag ID stored for GET by ID test:', firstId);", "    }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}, "response": []}, {"name": "6b. <PERSON> Handbag by ID", "event": [{"listen": "test", "script": {"exec": ["// Test 6b: Get Handbag by ID", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a single handbag object', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('object');", "    pm.expect(response<PERSON><PERSON>).to.have.property('handbagId').or.to.have.property('id');", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "    const globalToken = pm.globals.get('token');", "    pm.expect(globalToken).to.not.be.undefined;", "    console.log('✅ Using global token for request');", "});", "", "pm.test('Request method is GET', function () {", "    pm.expect(pm.request.method).to.eql('GET');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/handbags/{{existing_handbag_id}}", "host": ["{{base_url}}"], "path": ["api", "handbags", "{{existing_handbag_id}}"]}}}, {"name": "BONUS: <PERSON><PERSON> as Member (Limited Access)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: <PERSON><PERSON> as Member", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has token and member role', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('token');", "    pm.expect(responseJson).to.have.property('role');", "    pm.expect(responseJson.role).to.eql('member');", "});", "", "// Store member token for subsequent tests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    pm.environment.set('member_token', responseJson.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{member_email}}\",\n  \"password\": \"{{member_password}}\"\n}"}, "url": {"raw": "{{base_url}}/{{api_path}}/{{auth_endpoint}}", "host": ["{{base_url}}"], "path": ["{{api_path}}", "{{auth_endpoint}}"]}}}, {"name": "BONUS: Create Handbag with Member Token (Should Fail - 403)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: Unauthorized Create", "pm.test('Status code is 403 (Forbidden)', function () {", "    pm.response.to.have.status(403);", "});", "", "pm.test('Response indicates permission denied', function () {", "    // The API should return 403 for unauthorized access", "    pm.expect(pm.response.code).to.eql(403);", "});", "", "pm.test('JWT token was included in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.include('Bearer');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{member_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"modelName\": \"Unauthorized #Bag\",\n  \"material\": \"Leather\",\n  \"price\": 199.99,\n  \"stock\": 5,\n  \"brandId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}, {"name": "BONUS: Get Handbags without Token (Should Fail - 401)", "event": [{"listen": "test", "script": {"exec": ["// Bonus Test: Unauthorized Access", "pm.test('Status code is 401 (Unauthorized)', function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test('No Authorization header in request', function () {", "    pm.expect(pm.request.headers.get('Authorization')).to.be.undefined;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/handbags", "host": ["{{base_url}}"], "path": ["api", "handbags"]}}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default base URL if not set", "if (!pm.globals.get('base_url') && !pm.environment.get('base_url')) {", "    pm.globals.set('base_url', 'https://localhost:7176');", "    console.log('🔧 Set default base_url: https://localhost:7176');", "}", "", "// Set default test IDs if not set (will be overridden by dynamic values)", "if (!pm.environment.get('handbag_id_to_update')) {", "    pm.environment.set('handbag_id_to_update', '1');", "}", "if (!pm.environment.get('handbag_id_to_delete')) {", "    pm.environment.set('handbag_id_to_delete', '2');", "}", "if (!pm.environment.get('existing_handbag_id')) {", "    pm.environment.set('existing_handbag_id', '1');", "}", "", "// Check if token exists for authenticated requests", "const requestName = pm.info.requestName;", "const needsAuth = !requestName.includes('Login') && !requestName.includes('without Token');", "", "if (needsAuth) {", "    const globalToken = pm.globals.get('token');", "    if (!globalToken) {", "        console.log('⚠️  Warning: No global token found. Please run Login first!');", "        console.log('📝 Current request:', requestName);", "    } else {", "        console.log('✅ Global token available for:', requestName);", "        console.log('🔑 Token preview:', globalToken.substring(0, 20) + '...');", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "https://localhost:7176", "type": "string", "description": "Base URL for the API - Change this to match your server"}, {"key": "token", "value": "", "type": "string", "description": "JWT Token - Automatically set by Login Success test"}]}