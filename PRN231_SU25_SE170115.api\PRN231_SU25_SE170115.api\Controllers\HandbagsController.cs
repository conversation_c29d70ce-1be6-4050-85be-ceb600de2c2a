﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Model;
using Services;
using Middleware;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.OData.Query.Validator;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HandbagsController : ControllerBase
    {
        private readonly HangBagService _handbagService;

        public HandbagsController(HangBagService handbagService)
        {
            _handbagService = handbagService;
        }

        [HttpGet]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetAllHandbags()
        {
             var handbags = _handbagService.GetAllHandBag();
             return Ok(handbags);
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "administrator,moderator,developer,member")]
        public IActionResult GetHandbagById(int id)
        {
            try
            {
                var handbag = _handbagService.GetHandBagById(id);
                if (handbag == null)
                    throw new NotFoundException();

                return Ok(handbag);
            }
            catch (NotFoundException)
            {
                throw new NotFoundException();
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpPost]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult CreateHandbag([FromBody] CreateHandBagModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    throw new BadRequestException();

                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName) || model.Price <= 0 || model.Stock <= 0)
                    throw new BadRequestException();

                var result = _handbagService.CreateHandBag(model);
                if (!result)
                {
                    throw new BadRequestException();
                }

                return StatusCode(201, new { message = "Created successfully" });
            }
            catch (BadRequestException)
            {
                throw new BadRequestException();
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult UpdateHandbag(int id, [FromBody] UpdateHandBagModel model)
        {
            try
            {
                if (id <= 0)
                {
                    throw new BadRequestException();
                }

                var item = _handbagService.GetHandBagById(id);
                if (item == null)
                {
                    throw new NotFoundException();
                }

                if (!ModelState.IsValid)
                    throw new BadRequestException();


                var regex = new Regex(@"^([A-Z0-9][a-zA-Z0-9#]*\s)*([A-Z0-9][a-zA-Z0-9#]*)$");
                if (!regex.IsMatch(model.ModelName) || model.Price <= 0 || model.Stock <= 0)
                    throw new BadRequestException();

                var result = _handbagService.UpdateHandBag(id, model);
                if (!result)
                {
                    throw new BadRequestException();
                }

                return Ok(new { message = "Updated successfully" });
            }
            catch (BadRequestException)
            {
                throw new BadRequestException();
            }
            catch (NotFoundException)
            {
                throw new NotFoundException();
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "administrator,moderator")]
        public IActionResult DeleteHandbag(int id)
        {
            try
            {
                if (id <= 0)
                {
                    throw new BadRequestException();
                }

                var result = _handbagService.DeleteHandBag(id);
                if (!result)
                {
                    throw new NotFoundException();
                }

                return Ok(new { message = "Deleted successfully" });
            }
            catch (NotFoundException)
            {
                throw new NotFoundException();
            }
            catch (BadRequestException)
            {
                throw new BadRequestException();
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }

        [Authorize(Roles = "administrator,moderator,developer,member")]
        [HttpGet("search")]
        public IActionResult SearchHandbags([FromServices] ODataQueryOptions<ListHandBagModel> odataOptions, [FromQuery] string? modelName, [FromQuery] string? material)
        {
            var query = _handbagService.SearchWithProjection(modelName, material);

            var settings = new ODataValidationSettings();
            try
            {
                odataOptions.Validate(settings);

                var results = (IQueryable<ListHandBagModel>)odataOptions.ApplyTo(query);

                var filtered = results.ToList(); 

                var grouped = filtered
                    .GroupBy(h => h.BrandName)
                    .Select(g => new GroupedHandbagModel
                    {
                        BrandName = g.Key,
                        Handbags = g.ToList()
                    });
                return Ok(grouped);
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }

        }
    }
}
