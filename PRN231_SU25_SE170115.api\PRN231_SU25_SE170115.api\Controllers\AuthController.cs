﻿using Microsoft.AspNetCore.Mvc;
using Model;
using Services;
using Middleware;

namespace PRN231_SU25_SE170115.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly AuthService _authService;

        public AuthController(AuthService authService)
        {
            _authService = authService;
        }

        [HttpPost]
        public async Task<IActionResult> Login([FromBody] LoginModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                    throw new BadRequestException();

                var user = await _authService.Login(model);

                if (user == null)
                    throw new NotFoundException();

                var authResponse = await _authService.GenerateJwtToken(user.Username, user.Role);

                return Ok(new
                {
                    token = authResponse.Token,
                    role = authResponse.Role
                });
            }
            catch (BadRequestException)
            {
                throw new BadRequestException();
            }
            catch (NotFoundException)
            {
                throw new NotFoundException();
            }
            catch (Exception e)
            {
                throw new InternalServerErrorException();
            }
        }
    }
}
