#!/bin/bash

# API Testing Script for Handbag API
# Base URL
BASE_URL="http://localhost:5038"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counter
TOTAL_TESTS=0
PASSED_TESTS=0

# Function to print test results
print_test_result() {
    local test_name="$1"
    local expected_status="$2"
    local actual_status="$3"
    local response="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo -e "\n${BLUE}=== $test_name ===${NC}"
    echo -e "Expected Status: $expected_status"
    echo -e "Actual Status: $actual_status"
    
    if [[ "$actual_status" == "$expected_status" ]]; then
        echo -e "${GREEN}✅ PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ FAILED${NC}"
    fi
    
    echo -e "Response: $response"
    echo -e "${YELLOW}----------------------------------------${NC}"
}

# Function to extract token from response
extract_token() {
    echo "$1" | grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

echo -e "${BLUE}🚀 Starting API Tests for Handbag API${NC}"
echo -e "${BLUE}Base URL: $BASE_URL${NC}\n"

# Test 1: Authentication - Valid Login
echo -e "${YELLOW}Testing Authentication...${NC}"
AUTH_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "123456"}')

HTTP_STATUS=$(echo $AUTH_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $AUTH_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/auth - Valid Login" "200" "$HTTP_STATUS" "$RESPONSE_BODY"

# Extract token for subsequent tests
ADMIN_TOKEN=$(extract_token "$RESPONSE_BODY")
echo -e "Admin Token: ${ADMIN_TOKEN:0:50}..."

# Test 2: Authentication - Invalid Login
AUTH_FAIL_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "wrongpassword"}')

HTTP_STATUS=$(echo $AUTH_FAIL_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $AUTH_FAIL_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/auth - Invalid Login" "404" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 3: GET All Handbags - With Token
HANDBAGS_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

HTTP_STATUS=$(echo $HANDBAGS_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $HANDBAGS_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "GET /api/handbags - With Token" "200" "$HTTP_STATUS" "${RESPONSE_BODY:0:200}..."

# Extract first handbag ID for testing
FIRST_HANDBAG_ID=$(echo $RESPONSE_BODY | grep -o '"handbagId":[0-9]*' | head -1 | cut -d':' -f2)
echo -e "First Handbag ID: $FIRST_HANDBAG_ID"

# Test 4: GET All Handbags - Without Token
HANDBAGS_NO_TOKEN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags")

HTTP_STATUS=$(echo $HANDBAGS_NO_TOKEN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $HANDBAGS_NO_TOKEN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "GET /api/handbags - Without Token" "401" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 5: GET Handbag by ID - With Token
if [[ -n "$FIRST_HANDBAG_ID" ]]; then
    HANDBAG_BY_ID_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags/$FIRST_HANDBAG_ID" \
        -H "Authorization: Bearer $ADMIN_TOKEN")

    HTTP_STATUS=$(echo $HANDBAG_BY_ID_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $HANDBAG_BY_ID_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    print_test_result "GET /api/handbags/$FIRST_HANDBAG_ID - With Token" "200" "$HTTP_STATUS" "${RESPONSE_BODY:0:200}..."
fi

# Test 6: GET Handbag by ID - Invalid ID
HANDBAG_INVALID_ID_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags/99999" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

HTTP_STATUS=$(echo $HANDBAG_INVALID_ID_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $HANDBAG_INVALID_ID_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "GET /api/handbags/99999 - Invalid ID" "404" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 7: POST Create Handbag - Valid Data
CREATE_HANDBAG_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "Test Bag", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

HTTP_STATUS=$(echo $CREATE_HANDBAG_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $CREATE_HANDBAG_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/handbags - Valid Data" "201" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 8: POST Create Handbag - Invalid ModelName (doesn't match regex)
CREATE_INVALID_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "invalid model name", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

HTTP_STATUS=$(echo $CREATE_INVALID_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $CREATE_INVALID_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/handbags - Invalid ModelName" "400" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 9: POST Create Handbag - Invalid Price (negative)
CREATE_INVALID_PRICE_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "Test Bag", "material": "Leather", "price": -100, "stock": 15, "brandId": 1}')

HTTP_STATUS=$(echo $CREATE_INVALID_PRICE_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $CREATE_INVALID_PRICE_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/handbags - Invalid Price" "400" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 10: POST Create Handbag - Without Token
CREATE_NO_TOKEN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -d '{"modelName": "Test Bag", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

HTTP_STATUS=$(echo $CREATE_NO_TOKEN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $CREATE_NO_TOKEN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "POST /api/handbags - Without Token" "401" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 11: PUT Update Handbag - Valid Data
if [[ -n "$FIRST_HANDBAG_ID" ]]; then
    UPDATE_HANDBAG_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X PUT "$BASE_URL/api/handbags/$FIRST_HANDBAG_ID" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d '{"modelName": "Updated Test Bag", "material": "Premium Leather", "color": "Black", "price": 349.99, "stock": 20, "brandId": 1}')

    HTTP_STATUS=$(echo $UPDATE_HANDBAG_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $UPDATE_HANDBAG_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    print_test_result "PUT /api/handbags/$FIRST_HANDBAG_ID - Valid Data" "200" "$HTTP_STATUS" "$RESPONSE_BODY"
fi

# Test 12: PUT Update Handbag - Invalid ID
UPDATE_INVALID_ID_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X PUT "$BASE_URL/api/handbags/99999" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "Updated Bag", "material": "Leather", "price": 299.99, "stock": 15, "brandId": 1}')

HTTP_STATUS=$(echo $UPDATE_INVALID_ID_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $UPDATE_INVALID_ID_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "PUT /api/handbags/99999 - Invalid ID" "404" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 13: DELETE Handbag - Valid ID
# First create a handbag to delete
CREATE_FOR_DELETE_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -d '{"modelName": "ToDelete #Bag", "material": "Leather", "price": 100.00, "stock": 1, "brandId": 1}')

# Extract the created handbag ID (assuming it returns the ID in response)
# For now, we'll use a known ID or the last created one
DELETE_HANDBAG_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X DELETE "$BASE_URL/api/handbags/$FIRST_HANDBAG_ID" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

HTTP_STATUS=$(echo $DELETE_HANDBAG_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $DELETE_HANDBAG_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "DELETE /api/handbags/$FIRST_HANDBAG_ID - Valid ID" "200" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 14: DELETE Handbag - Invalid ID
DELETE_INVALID_ID_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X DELETE "$BASE_URL/api/handbags/99999" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

HTTP_STATUS=$(echo $DELETE_INVALID_ID_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $DELETE_INVALID_ID_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "DELETE /api/handbags/99999 - Invalid ID" "404" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 15: Search Handbags - With Parameters
SEARCH_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags/search?modelName=Galleria&material=Leather" \
    -H "Authorization: Bearer $ADMIN_TOKEN")

HTTP_STATUS=$(echo $SEARCH_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $SEARCH_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "GET /api/handbags/search - With Parameters" "200" "$HTTP_STATUS" "${RESPONSE_BODY:0:200}..."

# Test 16: Search Handbags - Without Token
SEARCH_NO_TOKEN_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags/search?modelName=Test")

HTTP_STATUS=$(echo $SEARCH_NO_TOKEN_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $SEARCH_NO_TOKEN_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

print_test_result "GET /api/handbags/search - Without Token" "401" "$HTTP_STATUS" "$RESPONSE_BODY"

# Test 17: Test Member Role Access (if member credentials exist)
echo -e "\n${YELLOW}Testing Member Role Access...${NC}"
MEMBER_AUTH_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/auth" \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>", "password": "123456"}')

HTTP_STATUS=$(echo $MEMBER_AUTH_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
RESPONSE_BODY=$(echo $MEMBER_AUTH_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

if [[ "$HTTP_STATUS" == "200" ]]; then
    MEMBER_TOKEN=$(extract_token "$RESPONSE_BODY")
    print_test_result "POST /api/auth - Member Login" "200" "$HTTP_STATUS" "$RESPONSE_BODY"

    # Test 18: Member tries to create handbag (should fail with 403)
    MEMBER_CREATE_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$BASE_URL/api/handbags" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $MEMBER_TOKEN" \
        -d '{"modelName": "Unauthorized #Bag", "material": "Leather", "price": 199.99, "stock": 5, "brandId": 1}')

    HTTP_STATUS=$(echo $MEMBER_CREATE_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $MEMBER_CREATE_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    print_test_result "POST /api/handbags - Member Role (Should Fail)" "403" "$HTTP_STATUS" "$RESPONSE_BODY"

    # Test 19: Member can read handbags (should succeed)
    MEMBER_READ_RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/api/handbags" \
        -H "Authorization: Bearer $MEMBER_TOKEN")

    HTTP_STATUS=$(echo $MEMBER_READ_RESPONSE | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    RESPONSE_BODY=$(echo $MEMBER_READ_RESPONSE | sed -e 's/HTTPSTATUS:.*//g')

    print_test_result "GET /api/handbags - Member Role" "200" "$HTTP_STATUS" "${RESPONSE_BODY:0:100}..."
else
    print_test_result "POST /api/auth - Member Login" "200" "$HTTP_STATUS" "$RESPONSE_BODY"
fi

echo -e "\n${BLUE}📊 Test Summary${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${NC}"
echo -e "Success Rate: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
