# Handbag API Tests - Postman Collection

## M<PERSON> tả
Collection này được tối ưu hóa để test API Handbag với JWT authentication. Sử dụng **biến toàn cục `token`** để lưu JWT token và tự động sử dụng cho tất cả requests.

## Cách sử dụng

### 1. Import Collection
- Import file `Postman_Collection_Handbag_API_Tests.json` vào Postman
- Collection sẽ tự động thiết lập các biến cần thiết

### 2. Thi<PERSON><PERSON> lập (T<PERSON><PERSON> chọn)
Chỉ cần thay đổi `base_url` nếu server của bạn chạy ở địa chỉ khác:
- Vào **Globals** hoặc **Environment**
- Thiết lập biến `base_url` (mặc định: `https://localhost:7176`)

### 3. Chạy Tests

#### Chạy tuần tự (Khuyến nghị):
1. **Login Success** - <PERSON><PERSON><PERSON> nhập và lưu token
2. **Create Handbag** - Tạo handbag mới và lưu ID
3. **Update Handbag** - Cập nhật handbag vừa tạo
4. **Delete Handbag** - Xóa handbag vừa tạo
5. **Get All Handbags** - Lấy danh sách và lưu ID đầu tiên
6. **Get Handbag by ID** - Lấy handbag theo ID

#### Chạy Collection Runner:
- Chọn toàn bộ collection và chạy
- Tests sẽ tự động sử dụng ID động từ các response

## Biến được sử dụng

### 🌐 Biến toàn cục (Global Variables):
- **`token`** - JWT token từ login (TỰ ĐỘNG lưu khi login thành công)
- `user_role` - Role của user (admin/member)
- `base_url` - URL của API server (có thể thay đổi)

### 📝 Biến environment (Tự động):
- `created_handbag_id` - ID của handbag vừa tạo
- `handbag_id_to_update` - ID để update (tự động từ create)
- `handbag_id_to_delete` - ID để delete (tự động từ create)
- `existing_handbag_id` - ID để test GET by ID (từ GET all)

### ⚙️ Biến có thể thay đổi:
- `base_url` - URL của API server (mặc định: https://localhost:7176)

## 🚀 Tính năng tối ưu

### 1. 🔑 Biến toàn cục Token
- **Login thành công tự động lưu JWT token vào biến toàn cục `{{token}}`**
- **Tất cả request sau tự động sử dụng `{{token}}` từ biến toàn cục**
- **Không cần copy/paste token thủ công**
- **Token được chia sẻ giữa tất cả collections và environments**

### 2. 📋 Tự động lưu ID
- Create handbag sẽ lưu ID cho update/delete
- Get all handbags sẽ lưu ID đầu tiên cho GET by ID

### 3. 📊 Logging thông minh
- Console log hiển thị token và ID được lưu
- Cảnh báo khi không có token
- Preview token trước khi gửi request
- Dễ debug khi có vấn đề

### 4. ✅ Validation đầy đủ
- Kiểm tra status code
- Kiểm tra response structure
- Kiểm tra JWT format
- Kiểm tra authorization header
- Xác minh token toàn cục tồn tại

## Test Cases

1. **Login Success** - Test đăng nhập thành công
2. **Login Failed** - Test đăng nhập thất bại
3. **Create Handbag** - Test tạo handbag mới (cần admin token)
4. **Update Handbag** - Test cập nhật handbag (cần admin token)
5. **Delete Handbag** - Test xóa handbag (cần admin token)
6. **Get All Handbags** - Test lấy danh sách handbag
7. **Get Handbag by ID** - Test lấy handbag theo ID

## Bonus Tests (Tùy chọn)
- Login as Member
- Unauthorized Create (member token)
- Get without Token (401 test)

## 📌 Lưu ý quan trọng

### 🔑 Về Token:
- **Token được lưu trong GLOBAL VARIABLES** - có thể sử dụng cho tất cả collections
- **Chỉ cần login 1 lần**, token sẽ tự động được sử dụng cho tất cả requests
- **Nếu token hết hạn**, chỉ cần chạy lại "Login Success"

### 🖥️ Về Server:
- Đảm bảo API server đang chạy trước khi test
- Thay đổi `base_url` nếu server chạy ở port khác
- Mặc định: `https://localhost:7176`

### 🔄 Về Test Order:
- **Bắt buộc chạy "Login Success" trước** để có token
- Các test khác có thể chạy theo thứ tự bất kỳ
- Kiểm tra Console log để debug nếu cần

### 🎯 Quick Start:
1. Import collection
2. Chạy "**1. Login Success**" → Token tự động lưu
3. Chạy bất kỳ test nào khác → Tự động dùng token
